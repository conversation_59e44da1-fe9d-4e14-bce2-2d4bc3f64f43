// Dark Mode Debug Script
// Run this in the browser console to test dark mode functionality

console.log('=== Dark Mode Debug Script ===');

// Check if elements exist
const themeToggle = document.getElementById('theme-toggle');
const themeIcon = document.getElementById('theme-icon');
const body = document.body;

console.log('Elements found:', {
    themeToggle: !!themeToggle,
    themeIcon: !!themeIcon,
    body: !!body
});

// Check current state
function checkCurrentState() {
    const hasLight = body.classList.contains('light');
    const hasDark = body.classList.contains('dark');
    const theme = localStorage.getItem('theme');
    const computedStyle = getComputedStyle(body);
    
    const state = {
        bodyClasses: body.className,
        hasLight,
        hasDark,
        localStorageTheme: theme,
        cssVars: {
            bgColor: computedStyle.getPropertyValue('--bg-color').trim(),
            textColor: computedStyle.getPropertyValue('--text-color').trim(),
            cardBg: computedStyle.getPropertyValue('--card-bg').trim(),
            borderColor: computedStyle.getPropertyValue('--border-color').trim()
        },
        actualStyles: {
            backgroundColor: computedStyle.backgroundColor,
            color: computedStyle.color
        }
    };
    
    console.log('Current state:', state);
    return state;
}

// Test theme toggle
function testToggle() {
    console.log('Testing theme toggle...');
    
    const beforeState = checkCurrentState();
    console.log('Before toggle:', beforeState);
    
    if (themeToggle) {
        themeToggle.click();
        
        setTimeout(() => {
            const afterState = checkCurrentState();
            console.log('After toggle:', afterState);
            
            const changed = beforeState.bodyClasses !== afterState.bodyClasses;
            console.log('Theme changed:', changed);
            
            if (!changed) {
                console.error('Theme toggle did not change body classes!');
            }
        }, 100);
    } else {
        console.error('Theme toggle button not found!');
    }
}

// Test manual class changes
function testManualToggle() {
    console.log('Testing manual class toggle...');
    
    const beforeState = checkCurrentState();
    
    if (body.classList.contains('dark')) {
        body.classList.remove('dark');
        body.classList.add('light');
        console.log('Manually switched to light');
    } else {
        body.classList.remove('light');
        body.classList.add('dark');
        console.log('Manually switched to dark');
    }
    
    setTimeout(() => {
        const afterState = checkCurrentState();
        console.log('After manual toggle:', afterState);
    }, 100);
}

// Check CSS custom properties
function checkCSSProperties() {
    console.log('Checking CSS custom properties...');
    
    const rootStyle = getComputedStyle(document.documentElement);
    const bodyStyle = getComputedStyle(body);
    
    const rootVars = {
        textLight: rootStyle.getPropertyValue('--text-light').trim(),
        textDark: rootStyle.getPropertyValue('--text-dark').trim(),
        bgLight: rootStyle.getPropertyValue('--bg-light').trim(),
        bgDark: rootStyle.getPropertyValue('--bg-dark').trim(),
        cardLight: rootStyle.getPropertyValue('--card-light').trim(),
        cardDark: rootStyle.getPropertyValue('--card-dark').trim()
    };
    
    const bodyVars = {
        textColor: bodyStyle.getPropertyValue('--text-color').trim(),
        bgColor: bodyStyle.getPropertyValue('--bg-color').trim(),
        cardBg: bodyStyle.getPropertyValue('--card-bg').trim()
    };
    
    console.log('Root CSS variables:', rootVars);
    console.log('Body CSS variables:', bodyVars);
    
    return { rootVars, bodyVars };
}

// Run all tests
function runAllTests() {
    console.log('=== Running All Dark Mode Tests ===');
    
    checkCurrentState();
    checkCSSProperties();
    
    console.log('Click the button below to test theme toggle:');
    console.log('testToggle()');
    
    console.log('Or test manual toggle:');
    console.log('testManualToggle()');
}

// Export functions to global scope for console use
window.darkModeDebug = {
    checkCurrentState,
    testToggle,
    testManualToggle,
    checkCSSProperties,
    runAllTests
};

// Auto-run initial tests
runAllTests();

console.log('=== Debug functions available ===');
console.log('darkModeDebug.checkCurrentState()');
console.log('darkModeDebug.testToggle()');
console.log('darkModeDebug.testManualToggle()');
console.log('darkModeDebug.checkCSSProperties()');
console.log('darkModeDebug.runAllTests()');
