<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Dark Mode Test</title>
    <style>
        :root {
            --text-light: #333;
            --text-dark: #f0f0f0;
            --bg-light: #ffffff;
            --bg-dark: #121212;
            --card-light: #f5f5f5;
            --card-dark: #1e1e1e;
        }

        body {
            --text-color: var(--text-light);
            --bg-color: var(--bg-light);
            --card-bg: var(--card-light);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            transition: background-color 0.3s, color 0.3s;
        }

        body.light {
            --text-color: var(--text-light);
            --bg-color: var(--bg-light);
            --card-bg: var(--card-light);
        }

        body.dark {
            --text-color: var(--text-dark);
            --bg-color: var(--bg-dark);
            --card-bg: var(--card-dark);
        }

        .card {
            background-color: var(--card-bg);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            transition: background-color 0.3s;
        }

        .toggle-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .toggle-btn:hover {
            background: #0056b3;
        }

        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Minimal Dark Mode Test</h1>
    
    <div class="status">
        Current Theme: <span id="theme-status">Loading...</span>
    </div>
    
    <div class="status">
        Body Classes: <span id="body-classes">Loading...</span>
    </div>
    
    <button id="toggle-btn" class="toggle-btn">Toggle Theme</button>
    
    <div class="card">
        <h2>Test Card</h2>
        <p>This card should change background color when you toggle the theme.</p>
        <p>Current background: <span id="bg-color">Loading...</span></p>
        <p>Current text color: <span id="text-color">Loading...</span></p>
    </div>

    <script>
        const body = document.body;
        const toggleBtn = document.getElementById('toggle-btn');
        const themeStatus = document.getElementById('theme-status');
        const bodyClasses = document.getElementById('body-classes');
        const bgColorSpan = document.getElementById('bg-color');
        const textColorSpan = document.getElementById('text-color');

        function updateStatus() {
            const isDark = body.classList.contains('dark');
            const isLight = body.classList.contains('light');
            
            themeStatus.textContent = isDark ? 'Dark' : (isLight ? 'Light' : 'Default');
            bodyClasses.textContent = body.className || 'none';
            
            const computedStyle = getComputedStyle(body);
            bgColorSpan.textContent = computedStyle.getPropertyValue('--bg-color').trim();
            textColorSpan.textContent = computedStyle.getPropertyValue('--text-color').trim();
            
            console.log('Theme updated:', {
                isDark,
                isLight,
                classes: body.className,
                bgColor: computedStyle.getPropertyValue('--bg-color').trim(),
                textColor: computedStyle.getPropertyValue('--text-color').trim()
            });
        }

        function toggleTheme() {
            console.log('Toggle clicked');
            if (body.classList.contains('dark')) {
                body.classList.remove('dark');
                body.classList.add('light');
                console.log('Switched to light');
            } else {
                body.classList.remove('light');
                body.classList.add('dark');
                console.log('Switched to dark');
            }
            updateStatus();
        }

        // Initialize
        body.classList.add('light');
        updateStatus();

        // Add event listener
        toggleBtn.addEventListener('click', toggleTheme);

        // Update status every second
        setInterval(updateStatus, 1000);
    </script>
</body>
</html>
