:root {
    --primary-color: #008000;
    --primary-dark: #006400;
    --primary-light: #90EE90;
    --text-light: #333;
    --text-dark: #f0f0f0;
    --bg-light: #ffffff;
    --bg-dark: #121212;
    --card-light: #f5f5f5;
    --card-dark: #1e1e1e;
    --transition-speed: 0.3s;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

body.light {
    --text-color: var(--text-light);
    --bg-color: var(--bg-light);
    --card-bg: var(--card-light);
}

body.dark {
    --text-color: var(--text-dark);
    --bg-color: var(--bg-dark);
    --card-bg: var(--card-dark);
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

.card {
    background-color: var(--card-bg);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -10px;
    width: 50px;
    height: 4px;
    background-color: var(--primary-color);
    transition: width 0.3s;
}

.section-title:hover::after {
    width: 100%;
}

.nav-link {
    position: relative;
    margin: 0 1rem;
    text-decoration: none;
    transition: color 0.3s;
}

.nav-link::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -5px;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: width 0.3s;
}

.nav-link:hover::after {
    width: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 10px;
}

/* Logo Fade-in Animation */
.logo-fade-in {
    animation: fadeInAnimation 1.5s ease-in-out;
    opacity: 0; /* Start fully transparent */
    animation-fill-mode: forwards; /* Stay at 100% opacity after animation */
}

@keyframes fadeInAnimation {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* Timeline */
.timeline {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 6px;
    background-color: var(--primary-color);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -3px;
}

.timeline-container {
    padding: 10px 40px;
    position: relative;
    background-color: inherit;
    width: 50%;
}

.timeline-container.left {
    left: 0;
}

.timeline-container.right {
    left: 50%;
}

.timeline-container::after {
    content: '';
    position: absolute;
    width: 25px;
    height: 25px;
    right: -17px;
    background-color: var(--bg-color);
    border: 4px solid var(--primary-color);
    top: 15px;
    border-radius: 50%;
    z-index: 1;
}

.timeline-container.left::after {
    right: -13px; /* Adjusted for visual centering */
}

.timeline-container.right::after {
    left: -13px; /* Adjusted for visual centering */
}

.timeline-content {
    padding: 20px 30px;
    background-color: var(--card-bg);
    position: relative;
    border-radius: 6px;
}

@media screen and (max-width: 768px) {
    .timeline::after {
        left: 31px;
    }

    .timeline-container {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }

    .timeline-container.left {
        left: 0;
    }

    .timeline-container.right {
        left: 0;
    }

    .timeline-container.left::after,
    .timeline-container.right::after {
        left: 15px;
    }
}

/* Button Animation */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: 0.5s;
}

.btn-animated:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: 0.5s;
}

.btn-animated:hover:after {
    width: 300px;
    height: 300px;
}

/* Additional Styles for Sticky Header */
.sticky-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    /* background-color: var(--bg-color); */ /* Ensure bg adapts */
    /* box-shadow: 0 2px 10px rgba(0,0,0,0.1); */ /* Softer shadow */
    transition: transform 0.3s ease-in-out, background-color var(--transition-speed), box-shadow 0.3s; /* Added background-color transition */
}

.header-shadow {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%; /* Start off-screen */
    width: 80%; /* Adjust as needed */
    max-width: 300px;
    height: 100%;
    background-color: var(--bg-light); /* Adapts to theme */
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 1001;
    transition: right 0.3s ease-in-out;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding: 2rem;
}

body.dark .mobile-menu {
    background-color: var(--bg-dark); /* Adapts to theme */
}

.mobile-menu.active {
    right: 0; /* Slide in */
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}
