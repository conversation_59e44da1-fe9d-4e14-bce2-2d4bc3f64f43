<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test - SAF</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--card-bg);
        }
        .test-item {
            margin: 1rem 0;
            padding: 0.5rem;
            background-color: var(--bg-color);
            border-radius: 4px;
            border: 1px solid var(--border-color);
        }
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #000;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- Test Header -->
    <header class="py-4 px-6 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200">Dark Mode Test</h1>
            <button id="theme-toggle" class="p-2 rounded-full bg-gray-200 dark:bg-gray-700 focus:outline-none">
                <i id="theme-icon" class="fas fa-moon text-gray-800 dark:text-yellow-300"></i>
            </button>
        </div>
    </header>

    <div class="container mx-auto px-6 py-8" style="background-color: var(--bg-color); color: var(--text-color); min-height: 100vh;">
        <!-- Theme Status Indicator -->
        <div class="test-section">
            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">Theme Status</h2>
            <div class="test-item">
                <p class="text-gray-700 dark:text-gray-300">Current Theme: <span id="current-theme" class="font-bold">Loading...</span></p>
                <p class="text-gray-700 dark:text-gray-300">Body Classes: <span id="body-classes" class="font-mono">Loading...</span></p>
                <button id="manual-toggle" class="mt-2 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Manual Toggle Test</button>
            </div>
        </div>

        <!-- CSS Custom Properties Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">CSS Custom Properties Test</h2>
            <div class="test-item">
                <p class="text-gray-700 dark:text-gray-300">Background Color: <span style="background-color: var(--bg-color); padding: 0.25rem; border: 1px solid var(--border-color);">var(--bg-color)</span></p>
            </div>
            <div class="test-item">
                <p class="text-gray-700 dark:text-gray-300">Text Color: <span style="color: var(--text-color); font-weight: bold;">var(--text-color)</span></p>
            </div>
            <div class="test-item">
                <p class="text-gray-700 dark:text-gray-300">Card Background: <span style="background-color: var(--card-bg); padding: 0.25rem; border: 1px solid var(--border-color);">var(--card-bg)</span></p>
            </div>
            <div class="test-item">
                <p class="text-gray-700 dark:text-gray-300">Border Color: <span style="border: 2px solid var(--border-color); padding: 0.25rem; display: inline-block;">var(--border-color)</span></p>
            </div>
            <div class="test-item">
                <p class="text-gray-700 dark:text-gray-300">Shadow Color: <span style="box-shadow: 0 4px 8px var(--shadow-color); padding: 0.25rem; display: inline-block;">var(--shadow-color)</span></p>
            </div>
        </div>

        <!-- Card Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">Card Component Test</h2>
            <div class="card p-6 rounded-xl shadow-md">
                <h3 class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-2">Test Card</h3>
                <p class="text-gray-600 dark:text-gray-400">This card should change background and text colors when switching themes.</p>
            </div>
        </div>

        <!-- Button Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">Button Test</h2>
            <button class="btn-animated bg-green-700 hover:bg-green-800 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 mr-4">
                <i class="fas fa-test mr-2"></i> Animated Button
            </button>
            <button class="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 py-3 px-6 rounded-lg">
                Theme-aware Button
            </button>
        </div>

        <!-- Mobile Menu Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">Mobile Menu Test</h2>
            <button id="test-mobile-menu" class="bg-blue-600 text-white py-2 px-4 rounded">Show Mobile Menu</button>
        </div>

        <!-- Footer Test -->
        <div class="test-section">
            <h2 class="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">Footer Test</h2>
            <footer class="bg-gray-900 text-white p-6 rounded-lg">
                <div class="border-t border-gray-800 pt-4">
                    <p class="text-gray-400">This footer should adapt to theme changes</p>
                    <input type="email" placeholder="Test email input" class="px-4 py-2 text-base rounded-lg focus:outline-none text-gray-800 mt-2">
                </div>
            </footer>
        </div>
    </div>

    <!-- Test Mobile Menu -->
    <div id="test-overlay" class="overlay"></div>
    <div id="test-mobile-menu-panel" class="mobile-menu">
        <div class="flex justify-between items-center mb-8">
            <h2 class="text-xl font-bold text-green-700 dark:text-green-500">Test Menu</h2>
            <button id="close-test-menu" class="p-2 rounded-md focus:outline-none text-gray-800 dark:text-gray-200">
                <i class="fas fa-times text-2xl"></i>
            </button>
        </div>
        <nav class="flex flex-col space-y-4">
            <a href="#" class="py-2 text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Test Link 1</a>
            <a href="#" class="py-2 text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">Test Link 2</a>
        </nav>
    </div>

    <script src="script.js"></script>
    <script src="debug_dark_mode.js"></script>
    <script>
        // Debug info display
        function updateDebugInfo() {
            const body = document.body;
            const debugInfo = document.getElementById('debug-info');
            const currentThemeSpan = document.getElementById('current-theme');
            const bodyClassesSpan = document.getElementById('body-classes');
            const hasLight = body.classList.contains('light');
            const hasDark = body.classList.contains('dark');
            const theme = localStorage.getItem('theme');

            if (debugInfo) {
                debugInfo.innerHTML = `
                    Body classes: ${body.className}<br>
                    Has light: ${hasLight}<br>
                    Has dark: ${hasDark}<br>
                    LocalStorage theme: ${theme}<br>
                    Current CSS vars:<br>
                    --bg-color: ${getComputedStyle(body).getPropertyValue('--bg-color')}<br>
                    --text-color: ${getComputedStyle(body).getPropertyValue('--text-color')}<br>
                    --card-bg: ${getComputedStyle(body).getPropertyValue('--card-bg')}
                `;
            }

            if (currentThemeSpan) {
                currentThemeSpan.textContent = hasDark ? 'Dark' : 'Light';
                currentThemeSpan.style.color = hasDark ? '#10b981' : '#3b82f6';
            }

            if (bodyClassesSpan) {
                bodyClassesSpan.textContent = body.className || 'none';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Create debug info element
            const debugDiv = document.createElement('div');
            debugDiv.id = 'debug-info';
            debugDiv.className = 'debug-info';
            document.body.appendChild(debugDiv);

            // Force update debug info immediately and periodically
            setTimeout(updateDebugInfo, 100);
            setInterval(updateDebugInfo, 1000);

            // Add manual toggle button for testing (uses the theme toggle from script.js)
            const manualToggle = document.getElementById('manual-toggle');
            const themeToggle = document.getElementById('theme-toggle');

            if (manualToggle && themeToggle) {
                manualToggle.addEventListener('click', function() {
                    console.log('Manual toggle clicked');
                    themeToggle.click(); // Trigger the main theme toggle
                });
            }

            // Additional test functionality
            const testMobileMenuBtn = document.getElementById('test-mobile-menu');
            const testOverlay = document.getElementById('test-overlay');
            const testMobileMenuPanel = document.getElementById('test-mobile-menu-panel');
            const closeTestMenu = document.getElementById('close-test-menu');

            if (testMobileMenuBtn) {
                testMobileMenuBtn.addEventListener('click', function() {
                    testOverlay.classList.add('active');
                    testMobileMenuPanel.classList.add('active');
                });
            }

            if (closeTestMenu) {
                closeTestMenu.addEventListener('click', function() {
                    testOverlay.classList.remove('active');
                    testMobileMenuPanel.classList.remove('active');
                });
            }

            if (testOverlay) {
                testOverlay.addEventListener('click', function() {
                    testOverlay.classList.remove('active');
                    testMobileMenuPanel.classList.remove('active');
                });
            }

            // Update debug info every second to monitor changes
            setInterval(updateDebugInfo, 1000);
        });
    </script>
</body>
</html>
